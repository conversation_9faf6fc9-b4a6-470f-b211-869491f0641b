#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复权计算测试和验证脚本
"""

import pandas as pd
import numpy as np
from tdx_data import get_code, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_adjustment_formula():
    """
    测试复权公式的正确性
    """
    print("=== 复权公式测试 ===")
    
    # 测试案例1：除权除息
    print("\n测试案例1：除权除息")
    print("假设：除权前收盘价=10元，每10股派红利2元，送股3股，配股2股，配股价5元")
    
    pre_close = 10.0
    cash_dividend = 2.0 / 10.0  # 每股红利 = 0.2元
    bonus_ratio = 3.0 / 10.0    # 送股比例 = 0.3
    rights_ratio = 2.0 / 10.0   # 配股比例 = 0.2
    rights_price = 5.0          # 配股价 = 5元
    
    # 标准复权公式
    numerator = pre_close - cash_dividend + rights_ratio * rights_price
    denominator = (1 + bonus_ratio + rights_ratio) * pre_close
    
    forward_factor = numerator / denominator
    backward_factor = denominator / numerator
    
    print(f"分子 = {pre_close} - {cash_dividend} + {rights_ratio} × {rights_price} = {numerator}")
    print(f"分母 = (1 + {bonus_ratio} + {rights_ratio}) × {pre_close} = {denominator}")
    print(f"前复权因子 = {numerator} / {denominator} = {forward_factor:.6f}")
    print(f"后复权因子 = {denominator} / {numerator} = {backward_factor:.6f}")
    print(f"验证（应该等于1）: {forward_factor * backward_factor:.10f}")
    
    # 除权后理论价格
    theoretical_price = numerator / (1 + bonus_ratio + rights_ratio)
    print(f"除权后理论价格 = {numerator} / {1 + bonus_ratio + rights_ratio} = {theoretical_price:.4f}元")
    
    # 测试案例2：仅送股
    print("\n测试案例2：仅送股")
    print("假设：除权前收盘价=20元，每10股送5股")
    
    pre_close2 = 20.0
    bonus_ratio2 = 5.0 / 10.0  # 送股比例 = 0.5
    
    forward_factor2 = pre_close2 / ((1 + bonus_ratio2) * pre_close2)
    backward_factor2 = (1 + bonus_ratio2) * pre_close2 / pre_close2
    
    print(f"前复权因子 = {pre_close2} / ({1 + bonus_ratio2} × {pre_close2}) = {forward_factor2:.6f}")
    print(f"后复权因子 = ({1 + bonus_ratio2} × {pre_close2}) / {pre_close2} = {backward_factor2:.6f}")
    print(f"除权后理论价格 = {pre_close2} / {1 + bonus_ratio2} = {pre_close2 / (1 + bonus_ratio2):.4f}元")
    
    # 测试案例3：仅分红
    print("\n测试案例3：仅分红")
    print("假设：除权前收盘价=15元，每10股派红利3元")
    
    pre_close3 = 15.0
    cash_dividend3 = 3.0 / 10.0  # 每股红利 = 0.3元
    
    forward_factor3 = (pre_close3 - cash_dividend3) / pre_close3
    backward_factor3 = pre_close3 / (pre_close3 - cash_dividend3)
    
    print(f"前复权因子 = ({pre_close3} - {cash_dividend3}) / {pre_close3} = {forward_factor3:.6f}")
    print(f"后复权因子 = {pre_close3} / ({pre_close3} - {cash_dividend3}) = {backward_factor3:.6f}")
    print(f"除息后理论价格 = {pre_close3} - {cash_dividend3} = {pre_close3 - cash_dividend3:.4f}元")

def test_stock_data():
    """
    测试实际股票数据的复权计算
    """
    print("\n=== 实际股票数据测试 ===")
    
    try:
        # 获取复权数据
        gbbq_reader = GbbqReader()
        gbbq_df = gbbq_reader.get_data()
        
        if gbbq_df.empty:
            print("警告：未获取到复权数据")
            return
        
        print(f"复权数据总条数：{len(gbbq_df)}")
        
        # 显示不同类型的复权事件统计
        category_counts = gbbq_df['category'].value_counts().sort_index()
        print("\n复权事件类型统计：")
        category_names = {
            1: "除权除息",
            2: "送配股上市", 
            3: "非流通股上市",
            4: "未知股本变动",
            5: "股本变化",
            6: "增发新股",
            7: "股份回购",
            8: "增发新股上市",
            9: "转配股上市",
            10: "可转债上市",
            11: "扩缩股",
            12: "非流通股缩股",
            13: "送认购权证",
            14: "送认沽权证"
        }
        
        for category, count in category_counts.items():
            name = category_names.get(category, f"未知类型{category}")
            print(f"  类型{category}({name}): {count}条")
        
        # 显示一些样本数据
        print("\n样本复权数据（前5条）：")
        sample_cols = ['market', 'code', 'datetime', 'category', 
                      'hongli_panqianliutong', 'peigujia_qianzongguben', 
                      'songgu_qianzongguben', 'peigu_houzongguben']
        print(gbbq_df[sample_cols].head())
        
        # 测试具体股票的复权计算
        test_codes = ['000001', '000002', '600000']
        for code in test_codes:
            stock_gbbq = gbbq_df[gbbq_df['code'] == code]
            if not stock_gbbq.empty:
                print(f"\n股票{code}的复权事件：")
                print(stock_gbbq[sample_cols])
                break
        
    except Exception as e:
        print(f"测试失败：{e}")

def compare_adjustment_methods():
    """
    比较修正前后的复权计算结果
    """
    print("\n=== 复权方法对比 ===")
    
    # 这里可以添加对比逻辑
    # 由于原始方法已被修正，这里主要展示新方法的特点
    
    print("修正后的复权计算特点：")
    print("1. 根据category类型区分处理不同的复权事件")
    print("2. 使用标准的复权公式计算除权除息")
    print("3. 正确处理股本变化、增发新股等事件")
    print("4. 前复权和后复权计算互为倒数关系")
    print("5. 保持了向量化操作的性能优势")

if __name__ == "__main__":
    print("复权计算测试和验证")
    print("=" * 50)
    
    # 运行测试
    test_adjustment_formula()
    test_stock_data()
    compare_adjustment_methods()
    
    print("\n测试完成！")
